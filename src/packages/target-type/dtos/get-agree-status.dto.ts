import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString } from 'class-validator';

export class GetAgreeStatusDto {
  @ApiProperty({
    description:
      'Array of unique identifiers for the targets to get agreement status',
    example: [
      'A70BB61B-47DF-4A4B-9525-36118C76AD8A',
      'B80CC62C-58EE-5B5C-C636-47229D87BE9B',
    ],
    type: [String],
  })
  @IsArray()
  @IsNotEmpty()
  @IsString({ each: true })
  targetUids: string[];
}
