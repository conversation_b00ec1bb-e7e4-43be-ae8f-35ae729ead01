import { Body, Controller, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { TargetTypeService } from './target-type.service';
import { TargetType } from '@ghq-abi/northstar-domain';
import { CreateTargetTypeDto, GetAgreeStatusDto } from './dtos';

@ApiTags('Target Type')
@Controller('target-type')
export class TargetTypeController {
  constructor(private readonly targetTypeService: TargetTypeService) {}

  @Post('feedback')
  @ApiOperation({
    summary: 'Create feedback target type for multiple targets',
  })
  @ApiResponse({
    status: 201,
    description: 'Target types created successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  public async createFeedback(@Body() body: CreateTargetTypeDto) {
    return this.targetTypeService.createTargetType(
      body.targetUids,
      TargetType.FEEDBACK,
      body.agree,
    );
  }

  @Post('final')
  @ApiOperation({
    summary: 'Create final target type for multiple targets',
  })
  @ApiResponse({
    status: 201,
    description: 'Target types created successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  public async createFinal(@Body() body: CreateTargetTypeDto) {
    return this.targetTypeService.createTargetType(
      body.targetUids,
      TargetType.FINAL,
      body.agree,
    );
  }

  @Post('agree-status')
  @ApiOperation({
    summary: 'Get agreement status for multiple targets by UIDs',
  })
  @ApiResponse({
    status: 200,
    description: 'Agreement status retrieved successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  public async getAgreeStatusByUids(@Body() body: GetAgreeStatusDto) {
    return this.targetTypeService.getAgreeStatusByUids(body.targetUids);
  }
}
